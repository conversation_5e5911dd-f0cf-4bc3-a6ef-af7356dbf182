[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-Zc:referenceBinding", "-Zc:__cplusplus", "-<PERSON>i", "-MDd", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m32", "--target=i686-pc-windows-msvc", "-clang:-std=c++14", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_PRINTSUPPORT_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\include", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\mqtt", "-ID:\\Qt\\5.15.2\\msvc2019\\include", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtPrintSupport", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtWidgets", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtGui", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtANGLE", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtNetwork", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug\\debug", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug", "-ID:\\Qt\\5.15.2\\msvc2019\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\main.cpp"], "directory": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/build/Desktop_Qt_5_15_2_MSVC2019_32bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-Zc:referenceBinding", "-Zc:__cplusplus", "-<PERSON>i", "-MDd", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m32", "--target=i686-pc-windows-msvc", "-clang:-std=c++14", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_PRINTSUPPORT_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\include", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\mqtt", "-ID:\\Qt\\5.15.2\\msvc2019\\include", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtPrintSupport", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtWidgets", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtGui", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtANGLE", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtNetwork", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug\\debug", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug", "-ID:\\Qt\\5.15.2\\msvc2019\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\mainwindow.cpp"], "directory": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/build/Desktop_Qt_5_15_2_MSVC2019_32bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-Zc:referenceBinding", "-Zc:__cplusplus", "-<PERSON>i", "-MDd", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m32", "--target=i686-pc-windows-msvc", "-clang:-std=c++14", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_PRINTSUPPORT_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\include", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\mqtt", "-ID:\\Qt\\5.15.2\\msvc2019\\include", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtPrintSupport", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtWidgets", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtGui", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtANGLE", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtNetwork", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug\\debug", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug", "-ID:\\Qt\\5.15.2\\msvc2019\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\qcustomplot.cpp"], "directory": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/build/Desktop_Qt_5_15_2_MSVC2019_32bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/qcustomplot.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-Zc:referenceBinding", "-Zc:__cplusplus", "-<PERSON>i", "-MDd", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m32", "--target=i686-pc-windows-msvc", "-clang:-std=c++14", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_PRINTSUPPORT_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\include", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\mqtt", "-ID:\\Qt\\5.15.2\\msvc2019\\include", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtPrintSupport", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtWidgets", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtGui", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtANGLE", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtNetwork", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug\\debug", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug", "-ID:\\Qt\\5.15.2\\msvc2019\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\mainwindow.h"], "directory": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/build/Desktop_Qt_5_15_2_MSVC2019_32bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-Zc:referenceBinding", "-Zc:__cplusplus", "-<PERSON>i", "-MDd", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m32", "--target=i686-pc-windows-msvc", "-clang:-std=c++14", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_PRINTSUPPORT_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\include", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\mqtt", "-ID:\\Qt\\5.15.2\\msvc2019\\include", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtPrintSupport", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtWidgets", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtGui", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtANGLE", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtNetwork", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug\\debug", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug", "-ID:\\Qt\\5.15.2\\msvc2019\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\qcustomplot.h"], "directory": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/build/Desktop_Qt_5_15_2_MSVC2019_32bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/qcustomplot.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-Zc:referenceBinding", "-Zc:__cplusplus", "-<PERSON>i", "-MDd", "-W3", "-w34100", "-w34189", "-w44996", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m32", "--target=i686-pc-windows-msvc", "-clang:-std=c++14", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DQT_DEPRECATED_WARNINGS", "-DQT_QML_DEBUG", "-DQT_PRINTSUPPORT_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\include", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\mqtt", "-ID:\\Qt\\5.15.2\\msvc2019\\include", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtPrintSupport", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtWidgets", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtGui", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtANGLE", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtNetwork", "-ID:\\Qt\\5.15.2\\msvc2019\\include\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug\\debug", "-IC:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug", "-ID:\\Qt\\5.15.2\\msvc2019\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Users\\<USER>\\Desktop\\智能电表\\QT_ShengFan (2)\\QT_ShengFan (2)\\QT_ShengFan\\ShengFan\\build\\Desktop_Qt_5_15_2_MSVC2019_32bit-Debug\\ui_mainwindow.h"], "directory": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/build/Desktop_Qt_5_15_2_MSVC2019_32bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/智能电表/QT_ShengFan (2)/QT_ShengFan (2)/QT_ShengFan/ShengFan/build/Desktop_Qt_5_15_2_MSVC2019_32bit-Debug/ui_mainwindow.h"}]
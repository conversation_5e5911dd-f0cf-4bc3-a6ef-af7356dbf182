#include "mainwindow.h"
#include "ui_mainwindow.h"

// Qt 5.15.2性能测试所需头文件
#include <QElapsedTimer>
#include <QDebug>


static int Location_Fornt,Location_Last;
QSharedPointer<QCPAxisTickerDateTime> dateTimeTicker(new QCPAxisTickerDateTime);
struct Volt_Data Home_1[99999];
static long int cnt_Old;
static long int cnt;
long int cnt_2;
static long int add_data_num = 0;

int pointCountX = 200;
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{

    this->setWindowTitle("盛帆杯");
    ui->setupUi(this);
        this->setWindowIcon(QIcon(":qrc/favicon.ico"));
    // 给widget绘图控件，设置个别名，方便书写
    pPlot1 = ui->plot1;

    // 状态栏指针
    sBar = statusBar();
    // 初始化图表1
    QPlot_init(pPlot1);

    client = new QMqttClient(this); // 考虑将client作为MainWindow的成员变量或指针
    client->setHostname("broker.mqttdashboard.com");
    client->setPort(1883);

    // 连接到host的槽函数，用于处理连接结果
    connect(client, &QMqttClient::connected, this, &MainWindow::onConnected);
    connect(client, &QMqttClient::disconnected, this, &MainWindow::onError);

    client->connectToHost();
    cnt_Old = QString::number(QDateTime::currentMSecsSinceEpoch() / 1000).toInt();
    cnt = QString::number(QDateTime::currentMSecsSinceEpoch() / 1000).toInt();

    // Qt 5.15.2特性验证
    qDebug() << "=== Qt 5.15.2环境验证开始 ===";
    bool qt5152Ok = verifyQt5152Features();
    if(qt5152Ok) {
        qDebug() << "✓ Qt 5.15.2环境验证通过，所有新特性可用";
    } else {
        qDebug() << "✗ Qt 5.15.2环境验证失败，请检查配置";
    }
    qDebug() << "=== Qt 5.15.2环境验证完成 ===";

    // 在连接成功后再尝试订阅
    // ...

}



MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::Data_Draw(const QMqttMessage &message)
{
    qDebug()<< message.payload();
    QString data_Original;
    data_Original = QString::fromUtf8(message.payload().data(), message.payload().size());
    Home_1[add_data_num].Vol_Effective = Data_Take_Double(data_Original,"A","?");
    Home_1[add_data_num].Electricity_Effective = Data_Take_Double(data_Original,"B","?");
    Home_1[add_data_num].Power = Data_Take_Double(data_Original,"C","?");
    Home_1[add_data_num].Energy_Total = Data_Take_Double(data_Original,"D","?");
    Home_1[add_data_num].Frequency = Data_Take_Double(data_Original,"E","?");
    Location_Fornt = 0;
    Location_Last = 0;
    qDebug()<< Home_1[add_data_num].Vol_Effective;
    qDebug()<< Home_1[add_data_num].Electricity_Effective;
    qDebug()<< Home_1[add_data_num].Power;
    qDebug()<< Home_1[add_data_num].Energy_Total;
    qDebug()<< Home_1[add_data_num].Frequency;
    add_data_num++;
    add_data_num%=99999;
    qDebug()<< data_Original;
}
//int Vol_Effective;              //A
//int Electricity_Effective;      //B
//int Power;                      //C
//int Energy_Total;               //D
//int Frequency;                  //E

void MainWindow::onConnected()
{
    QMqttTopicFilter filter("nitamade/jiushe/ge/shabi");
    QMqttSubscription* subscription = client->subscribe(filter);
    if (subscription)
    {
        connect(subscription, &QMqttSubscription::messageReceived, this, &MainWindow::Data_Draw);
    }
    else
    {


    }
}

void MainWindow::onError() {
    qDebug()<< "错误";
    // 处理错误情况
}

void MainWindow::QPlot_init(QCustomPlot *customPlot)
{

     //创建定时器，用于定时生成曲线坐标点数据
    QTimer *timer = new QTimer(this);
    timer->start(1000);
    connect(timer,SIGNAL(timeout()),this,SLOT(TimeData_Update()));


     //图表添加两条曲线

    pGraph1_1 = customPlot->addGraph();
    pGraph1_2 = customPlot->addGraph();
    qDebug()<< "ok";
    // 设置曲线颜色
    pGraph1_1->setPen(QPen(Qt::black));
    pGraph1_2->setPen(QPen(Qt::white));

     //设置坐标轴名称
    customPlot->xAxis->setLabel("X");
    customPlot->yAxis->setLabel("电压有效值/V");

    // 设置y坐标轴显示范围
    customPlot->yAxis->setRange(0,500);

     //显示图表的图例
    customPlot->legend->setVisible(true);  
    pGraph1_2->setVisible(false);
    pGraph1_2->removeFromLegend();
     //添加曲线名称

    pGraph1_1->setName("电压有效值");

    customPlot->xAxis->setLabel("时间");





    // 设置时间格式
    dateTimeTicker->setDateTimeFormat("yyyy-MM-dd hh:mm:ss");

    // 设置时间轴刻度的数量（注意：这只是一个建议值，QCustomPlot 可能会根据实际的范围调整它）
    dateTimeTicker->setTickCount(12); // 或者使用 setTickStepStrategy 来自动计算合适的步长

    // 设置刻度标签的旋转角度（如果需要的话）
    customPlot->xAxis->setTickLabelRotation(35);

    // 设置 tick 策略为尝试满足设置的刻度数量
    dateTimeTicker->setTickStepStrategy(QCPAxisTicker::tssMeetTickCount);

    // 设置 X 轴的 ticker
    customPlot->xAxis->setTicker(dateTimeTicker);

    // 获取系统当前时间
    QDateTime now = QDateTime::currentDateTime();

    // 设置 X 轴的范围为当前时间前后的一段时间（这里假设你想要显示当前时间前后各一小时的范围）
    QDateTime oneHourBefore = now.addSecs(0);
    QDateTime oneHourAfter = now.addSecs(60);
    customPlot->xAxis->setRange(oneHourBefore.toMSecsSinceEpoch()/1000.0, oneHourAfter.toMSecsSinceEpoch()/1000.0);

    // 确保 X 轴是时间轴类型
      //customPlot->xAxis->setType(QCPAxis::atTime);



     //允许用户用鼠标拖动轴范围，用鼠标滚轮缩放，点击选择图形:
    customPlot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectPlottables);
}
// 定时器溢出处理槽函数。用来生成曲线的坐标数据。
void MainWindow::TimeData_Update(void)
{
    static double data_to_picture;
    if(add_data_num >(cnt-cnt_Old))
    {
        switch(ui->Label->currentIndex())
        {
            case 0:data_to_picture = Home_1[cnt-cnt_Old].Vol_Effective;        break;
            case 1:data_to_picture = Home_1[cnt-cnt_Old].Electricity_Effective;break;
            case 2:data_to_picture = Home_1[cnt-cnt_Old].Power;                break;
            case 3:data_to_picture = Home_1[cnt-cnt_Old].Energy_Total;         break;
            case 4:data_to_picture = Home_1[cnt-cnt_Old].Frequency;            break;
            default :break;
        }
        qDebug()<<"cnt-cnt_Old:";
        qDebug()<<cnt-cnt_Old;
        qDebug()<<data_to_picture;
        Show_Plot(pPlot1, 1.0*data_to_picture);

    }
}

//曲线更新绘图
void MainWindow::Show_Plot(QCustomPlot *customPlot, double num)
{

    pGraph1_1->addData(cnt, num);
    pGraph1_2->addData(cnt, num*-1);

    cnt++;
    // 设置x坐标轴显示范围，使其自适应缩放x轴，x轴最大显示1000个点
//        customPlot->xAxis->setRange((cnt>60)?(cnt-60):cnt_Old, cnt);
    if(ui->checkBox->checkState())
          customPlot->xAxis->setRange((cnt-cnt_Old)<60?cnt_Old:cnt-60, cnt);
    if(ui->checkBox_2->checkState())
    {
         customPlot->graph(0)->rescaleValueAxis();// y轴自适应，可放大可缩小
    }
//    else{
//           customPlot->xAxis->setRange(pointCountX-60,pointCountX);
//        }
    // 更新绘图，这种方式在高填充下太浪费资源。有另一种方式rpQueuedReplot，可避免重复绘图。
    // 最好的方法还是将数据填充、和更新绘图分隔开。将更新绘图单独用定时器更新。例程数据量较少没用单独定时器更新，实际工程中建议大家加上。
    //customPlot->replot();
    customPlot->replot(QCustomPlot::rpQueuedReplot);

    static QTime time(QTime::currentTime());
    double key = time.elapsed()/1000.0; // 开始到现在的时间，单位秒
    ////计算帧数
    static double lastFpsKey;
    static int frameCount;
    ++frameCount;
    if (key-lastFpsKey > 1) // 每1秒求一次平均值
    {
        //状态栏显示帧数和数据总数
        ui->statusbar->showMessage(
            QString("%1 FPS, Total Data points: %2")
            .arg(frameCount/(key-lastFpsKey), 0, 'f', 0)
            .arg(customPlot->graph(0)->data()->size()+customPlot->graph(1)->data()->size())
            , 0);
        lastFpsKey = key;
        frameCount = 0;
    }

}

void MainWindow::Show_Plot1(QCustomPlot *customPlot, double num)
{
    pGraph1_1->addData(cnt_2, num);
    pGraph1_2->addData(cnt_2, num*-1);

    cnt_2++;
    // 设置x坐标轴显示范围，使其自适应缩放x轴，x轴最大显示1000个点
  if(ui->checkBox->checkState())
      customPlot->xAxis->setRange((cnt_2-cnt_Old)<60?cnt_Old:cnt_2-60, cnt_2);

//  else{
//         customPlot->xAxis->setRange(pointCountX-60,pointCountX);
//      }
    // 更新绘图，这种方式在高填充下太浪费资源。有另一种方式rpQueuedReplot，可避免重复绘图。
    // 最好的方法还是将数据填充、和更新绘图分隔开。将更新绘图单独用定时器更新。例程数据量较少没用单独定时器更新，实际工程中建议大家加上。
    //customPlot->replot();
    customPlot->replot(QCustomPlot::rpQueuedReplot);

    static QTime time(QTime::currentTime());
    double key = time.elapsed()/1000.0; // 开始到现在的时间，单位秒
    ////计算帧数
    static double lastFpsKey;
    static int frameCount;
    ++frameCount;
    if (key-lastFpsKey > 1) // 每1秒求一次平均值
    {
        //状态栏显示帧数和数据总数
        ui->statusbar->showMessage(
            QString("%1 FPS, Total Data points: %2")
            .arg(frameCount/(key-lastFpsKey), 0, 'f', 0)
            .arg(customPlot->graph(0)->data()->size()+customPlot->graph(1)->data()->size())
            , 0);
        lastFpsKey = key;
        frameCount = 0;
    }

}
double MainWindow::Data_Take_Double(QString Data,QString Data_name,QString Data_Last)
{
    double Value;
        Location_Fornt = Data.indexOf(Data_name,0,Qt::CaseInsensitive);  
        Location_Last = Data.indexOf(Data_Last,Location_Last+1,Qt::CaseInsensitive);
        Value = Data.mid((Location_Fornt+Data_name.length()),Location_Last-(Location_Fornt+Data_name.length())).toDouble();
        return Value;
}

void MainWindow::on_Label_currentIndexChanged(int index)
{
    double data_to_picture;
    pPlot1->graph(0)->data().data()->clear(); // 仅仅清除曲线的数据
    pPlot1->replot(QCustomPlot::rpQueuedReplot);
    cnt_2 = cnt_Old;
    switch(index)
    {
        case 0:
                    pPlot1->yAxis->setLabel("电压有效值/V");
                    pGraph1_1->setName("电压有效值");        break;
        case 1:    pPlot1->yAxis->setLabel("电流有效值/A");
                   pGraph1_1->setName("电流有效值");   break;

        case 2:    pPlot1->yAxis->setLabel("频率/Hz");
                   pGraph1_1->setName("频率"); break;

        case 3:    pPlot1->yAxis->setLabel("功率/W");
                   pGraph1_1->setName("功率"); break;

        case 4:    pPlot1->yAxis->setLabel("能量消耗/KWh");
                   pGraph1_1->setName("能量消耗");  break;
        default :break;
    }
    while(add_data_num-(cnt_2-cnt_Old) != 0)
    {
        switch(index)
        {
            case 0:data_to_picture = Home_1[cnt_2-cnt_Old].Vol_Effective;        break;
            case 1:data_to_picture = Home_1[cnt_2-cnt_Old].Electricity_Effective;break;
            case 2:data_to_picture = Home_1[cnt_2-cnt_Old].Power;                break;
            case 3:data_to_picture = Home_1[cnt_2-cnt_Old].Energy_Total;         break;
            case 4:data_to_picture = Home_1[cnt_2-cnt_Old].Frequency;            break;
            default :break;
        }
        qDebug()<< ui->Label->currentIndex();
        Show_Plot1(pPlot1, 1.0*data_to_picture);
    }
    cnt = cnt_2;
}

void MainWindow::on_checkBox_stateChanged(int arg1)
{
    pointCountX = cnt;
}

// Qt 5.15.2特性验证函数实现
bool MainWindow::verifyQt5152Features()
{
    qDebug() << "开始验证Qt 5.15.2特性...";

    bool mqttOk = testMqttClientEnhancements();
    bool jsonOk = testJsonPerformance();
    bool cryptoOk = testCryptoEnhancements();

    bool allOk = mqttOk && jsonOk && cryptoOk;
    qDebug() << "Qt 5.15.2特性验证结果:" << (allOk ? "通过" : "失败");

    return allOk;
}

bool MainWindow::testMqttClientEnhancements()
{
    qDebug() << "测试MQTT客户端增强功能...";

    try {
        // 测试Qt 5.15.2的MQTT客户端新特性
        QMqttClient testClient;

        // 验证MQTT 5.0协议支持
        testClient.setProtocolVersion(QMqttClient::MQTT_5_0);
        qDebug() << "MQTT 5.0协议支持: 可用";

        // 验证增强的keepAlive设置
        testClient.setKeepAlive(60);
        qDebug() << "KeepAlive设置: 正常";

        // 验证SSL配置改进
        QSslConfiguration sslConfig = QSslConfiguration::defaultConfiguration();
        qDebug() << "SSL配置: 可用";

        return true;
    } catch (...) {
        qDebug() << "MQTT客户端测试失败";
        return false;
    }
}

bool MainWindow::testJsonPerformance()
{
    qDebug() << "测试JSON处理性能改进...";

    try {
        // 测试OneNET JSON格式解析
        QString testJson = R"({
            "id": "1",
            "version": "1.0",
            "params": {
                "voltage": 220.5,
                "current": 1.2,
                "frequency": 50.0,
                "active_power": 264.6,
                "total_energy": 1000.0
            }
        })";

        QElapsedTimer timer;
        timer.start();

        // 执行多次解析测试性能
        for(int i = 0; i < 1000; ++i) {
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(testJson.toUtf8(), &error);

            if(error.error != QJsonParseError::NoError) {
                qDebug() << "JSON解析错误:" << error.errorString();
                return false;
            }

            QJsonObject root = doc.object();
            QJsonObject params = root["params"].toObject();

            // 验证数据提取
            double voltage = params["voltage"].toDouble();
            double current = params["current"].toDouble();
        }

        qint64 elapsed = timer.elapsed();
        qDebug() << "JSON处理性能测试完成, 耗时:" << elapsed << "ms";
        qDebug() << "平均每次解析:" << (elapsed / 1000.0) << "ms";

        return true;
    } catch (...) {
        qDebug() << "JSON性能测试失败";
        return false;
    }
}

bool MainWindow::testCryptoEnhancements()
{
    qDebug() << "测试加密功能增强...";

    try {
        // 测试MD5加密性能改进
        QString testData = "version=2018-10-31&res=products/cF16DWy2B8/devices/stm32f103&et=2066666954&method=md5";

        QElapsedTimer timer;
        timer.start();

        // 执行多次加密测试性能
        for(int i = 0; i < 1000; ++i) {
            QCryptographicHash hash(QCryptographicHash::Md5);
            hash.addData(testData.toUtf8());

            // 测试Qt 5.15.2的Base64UrlEncoding新特性
            QByteArray result = hash.result().toBase64(QByteArray::Base64UrlEncoding);
        }

        qint64 elapsed = timer.elapsed();
        qDebug() << "加密性能测试完成, 耗时:" << elapsed << "ms";
        qDebug() << "平均每次加密:" << (elapsed / 1000.0) << "ms";

        // 验证Base64UrlEncoding特性
        QByteArray testArray = "test";
        QByteArray encoded = testArray.toBase64(QByteArray::Base64UrlEncoding);
        qDebug() << "Base64UrlEncoding特性: 可用";

        return true;
    } catch (...) {
        qDebug() << "加密功能测试失败";
        return false;
    }
}

#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTcpServer>
#include <QTcpSocket>
#include <QLabel>
#include <QMessageBox>
#include <QString>
#include <QTimer>
#include <QPainter>
#include "qcustomplot.h"
#include "QtMqtt/qmqttclient.h"
#include "mqtt/qmqtt.h"
#include <QDateTime>

// Qt 5.15.2增强功能支持
#include <QJsonDocument>      // JSON处理性能优化
#include <QJsonObject>        // JSON对象操作
#include <QCryptographicHash> // 加密算法增强
#include <QSslConfiguration>  // SSL/TLS配置改进

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();



public slots:
    void TimeData_Update(void);

private slots:
    void on_Label_currentIndexChanged(int index);

    void on_checkBox_stateChanged(int arg1);

private:
    Ui::MainWindow *ui;

    // 绘图控件的指针
    QCustomPlot *pPlot1;
    // 状态栏指针
    QStatusBar *sBar;
    // 绘图控件中曲线的指针
    QCPGraph *pGraph1_1;
    QCPGraph *pGraph1_2;

    QMqttClient * client;
    QMQTT::Client *m_client;
    // 定义MQTT主题和消息内容
    void Data_Draw(const QMqttMessage &message);
    void onConnected();
    void onError();
    void QPlot_init(QCustomPlot *customPlot);
    void Show_Plot(QCustomPlot *customPlot, double num);
    void Show_Plot1(QCustomPlot *customPlot, double num);
    double Data_Take_Double(QString Data,QString Data_name,QString Data_Last);

    // Qt 5.15.2特性验证函数
    bool verifyQt5152Features();     // 验证Qt 5.15.2新特性
    bool testMqttClientEnhancements(); // 测试MQTT客户端改进
    bool testJsonPerformance();      // 测试JSON处理性能
    bool testCryptoEnhancements();   // 测试加密功能增强
};
struct Volt_Data
{
    double Vol_Effective;              //A
    double Electricity_Effective;      //B
    double Power;                      //C
    double Energy_Total;               //D
    double Frequency;                  //E
};

#endif // MAINWINDOW_H
